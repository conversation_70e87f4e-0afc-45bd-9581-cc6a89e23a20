# Pinball Directory API

A FastAPI-based REST API for managing pinball machines, their locations, and maintenance records.

## Setup

### Prerequisites

- Python 3.11+
- Poetry for dependency management
- PostgreSQL database
- Doppler CLI for secrets management

### Installation

1. Install Poetry:
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

2. Install dependencies:
```bash
poetry install
```

3. Install Doppler CLI and configure secrets:
```bash
# Install Doppler CLI (Linux)
curl -Ls --tlsv1.2 --proto "=https" --retry 3 https://cli.doppler.com/install.sh | sh

# Initialize Doppler
doppler login
doppler setup
```

4. Configure the following secrets in Doppler:
```
PINBALL_DB_HOST=digital-ocean url
PINBALL_DB_ADMIN=pinball_admin
PINBALL_DB_PASSWORD=your_secure_password
PINBALL_DB_NAME=pinball
PINBALL_DB_PORT=25060
```

5. Create the database:
```bash
# Run the database creation scripts
psql -U postgres -f database/create_database.sql
psql -U pinball_admin -d pinball -f database/create_pinball_tables.sql
```

### Running the Application

1. Start the API server:
```bash
doppler run -- poetry run uvicorn app.main:app --reload
```

2. Access the API documentation:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API Endpoints

### Pinball Machines
- `GET /api/v1/machines/` - List all pinball machines
- `POST /api/v1/machines/` - Create a new pinball machine
- `GET /api/v1/machines/{machine_id}` - Get a specific pinball machine
- `PUT /api/v1/machines/{machine_id}` - Update a pinball machine
- `DELETE /api/v1/machines/{machine_id}` - Delete a pinball machine

### Locations
- `GET /api/v1/locations/` - List all locations
- `POST /api/v1/locations/` - Add a new location
- `GET /api/v1/locations/{location_id}` - Get a specific location
- `PUT /api/v1/locations/{location_id}` - Update a location
- `DELETE /api/v1/locations/{location_id}` - Delete a location

### Maintenance
- `GET /api/v1/maintenance/` - List all maintenance records
- `POST /api/v1/maintenance/` - Create a new maintenance record
- `GET /api/v1/maintenance/{maintenance_id}` - Get a specific maintenance record
- `PUT /api/v1/maintenance/{maintenance_id}` - Update a maintenance record
- `DELETE /api/v1/maintenance/{maintenance_id}` - Delete a maintenance record

## Development

### Project Structure
```
pinball-directory/
├── app/
│   ├── api/
│   │   └── v1/
│   │       └── endpoints/
│   │           └── pinball.py
│   ├── core/
│   │   ├── config.py
│   │   └── database.py
│   ├── crud/
│   │   └── pinball.py
│   ├── models/
│   │   └── pinball.py
│   ├── schemas/
│   │   └── pinball.py
│   └── main.py
├── database/
│   ├── create_database.sql
│   └── create_pinball_tables.sql
├── doppler.yaml
├── pyproject.toml
└── README.md
```
