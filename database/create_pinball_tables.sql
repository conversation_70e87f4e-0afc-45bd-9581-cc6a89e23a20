-- Pinball Infrastructure Database Tables
-- This file creates the core tables for managing pinball machines, their locations, and maintenance records

-- Set the schema context
SET search_path TO pinball;

-- Table: pinball_machine
-- Stores information about individual pinball machines
CREATE TABLE pinball_machine (
    id SERIAL PRIMARY KEY,
    machine_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    manufacturer VA<PERSON><PERSON><PERSON>(100),
    year_manufactured INTEGER,
    model VARCHAR(100),
    serial_number VARCHAR(100) UNIQUE,
    theme VARCHAR(255),
    game_type VARCHAR(50), -- e.g., 'electromechanical', 'solid_state', 'digital'
    player_count INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    purchase_date DATE,
    purchase_price DECIMAL(10,2),
    current_value DECIMAL(10,2),
    condition_rating INTEGER CHECK (condition_rating >= 1 AND condition_rating <= 10),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: pinball_machine_location
-- Tracks where pinball machines are located and their location history
CREATE TABLE pinball_machine_location (
    id SERIAL PRIMARY KEY,
    machine_id INTEGER NOT NULL REFERENCES pinball_machine(id) ON DELETE CASCADE,
    location_name VARCHAR(255) NOT NULL,
    location_type VARCHAR(50), -- e.g., 'arcade', 'home', 'warehouse', 'repair_shop'
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state_province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'USA',
    contact_person VARCHAR(255),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(255),
    placement_date DATE NOT NULL,
    removal_date DATE,
    is_current_location BOOLEAN DEFAULT true,
    location_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure only one current location per machine
    CONSTRAINT unique_current_location_per_machine 
        EXCLUDE (machine_id WITH =) WHERE (is_current_location = true)
);

-- Table: pinball_machine_maintenance
-- Records maintenance activities, repairs, and service history
CREATE TABLE pinball_machine_maintenance (
    id SERIAL PRIMARY KEY,
    machine_id INTEGER NOT NULL REFERENCES pinball_machine(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(50) NOT NULL, -- e.g., 'routine', 'repair', 'upgrade', 'inspection'
    maintenance_date DATE NOT NULL,
    technician_name VARCHAR(255),
    technician_company VARCHAR(255),
    description TEXT NOT NULL,
    parts_replaced TEXT,
    parts_cost DECIMAL(10,2),
    labor_cost DECIMAL(10,2),
    total_cost DECIMAL(10,2),
    maintenance_status VARCHAR(20) DEFAULT 'completed', -- e.g., 'scheduled', 'in_progress', 'completed', 'cancelled'
    priority_level VARCHAR(20) DEFAULT 'normal', -- e.g., 'low', 'normal', 'high', 'urgent'
    before_condition_rating INTEGER CHECK (before_condition_rating >= 1 AND before_condition_rating <= 10),
    after_condition_rating INTEGER CHECK (after_condition_rating >= 1 AND after_condition_rating <= 10),
    warranty_work BOOLEAN DEFAULT false,
    next_maintenance_due DATE,
    maintenance_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX idx_pinball_machine_manufacturer ON pinball_machine(manufacturer);
CREATE INDEX idx_pinball_machine_year ON pinball_machine(year_manufactured);
CREATE INDEX idx_pinball_machine_active ON pinball_machine(is_active);
CREATE INDEX idx_pinball_machine_serial ON pinball_machine(serial_number);

CREATE INDEX idx_location_machine_id ON pinball_machine_location(machine_id);
CREATE INDEX idx_location_current ON pinball_machine_location(is_current_location);
CREATE INDEX idx_location_placement_date ON pinball_machine_location(placement_date);
CREATE INDEX idx_location_type ON pinball_machine_location(location_type);

CREATE INDEX idx_maintenance_machine_id ON pinball_machine_maintenance(machine_id);
CREATE INDEX idx_maintenance_date ON pinball_machine_maintenance(maintenance_date);
CREATE INDEX idx_maintenance_type ON pinball_machine_maintenance(maintenance_type);
CREATE INDEX idx_maintenance_status ON pinball_machine_maintenance(maintenance_status);
CREATE INDEX idx_maintenance_next_due ON pinball_machine_maintenance(next_maintenance_due);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update the updated_at column
CREATE TRIGGER update_pinball_machine_updated_at 
    BEFORE UPDATE ON pinball_machine 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pinball_machine_location_updated_at 
    BEFORE UPDATE ON pinball_machine_location 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pinball_machine_maintenance_updated_at 
    BEFORE UPDATE ON pinball_machine_maintenance 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments to tables and important columns for documentation
COMMENT ON TABLE pinball_machine IS 'Core table storing information about individual pinball machines';
COMMENT ON COLUMN pinball_machine.condition_rating IS 'Rating from 1-10 where 10 is perfect condition';
COMMENT ON COLUMN pinball_machine.game_type IS 'Type of pinball machine: electromechanical, solid_state, or digital';

COMMENT ON TABLE pinball_machine_location IS 'Tracks current and historical locations of pinball machines';
COMMENT ON COLUMN pinball_machine_location.is_current_location IS 'Only one record per machine should have this set to true';

COMMENT ON TABLE pinball_machine_maintenance IS 'Records all maintenance activities, repairs, and service history';
COMMENT ON COLUMN pinball_machine_maintenance.maintenance_type IS 'Type of maintenance: routine, repair, upgrade, or inspection';
COMMENT ON COLUMN pinball_machine_maintenance.priority_level IS 'Priority level: low, normal, high, or urgent';
