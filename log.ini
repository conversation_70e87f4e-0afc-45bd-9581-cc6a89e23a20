[loggers]
keys=root,console

[handlers]
keys=consoleHandler

[formatters]
keys=logfileformatter

[logger_root]
level=INFO
handlers=consoleHandler

[logger_console]
level=DEBUG
handlers=consoleHandler
qualname=console
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=logfileformatter
args=(sys.stdout,)

[formatter_logfileformatter]
format=[%(asctime)s.%(msecs)03d] [%(levelname)s] [%(filename)s:%(lineno)d]: %(message)s
