version: '3.8'

services:
  pinball-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pinball-api
    environment:
      # These will be populated by <PERSON><PERSON><PERSON> when you run with doppler run --
      DATABASE_URL: ${DATABASE_URL}
      DATABASE_HOST: ${DATABASE_HOST:-postgres}
      DATABASE_PORT: ${DATABASE_PORT:-5432}
      DATABASE_NAME: ${DATABASE_NAME:-pinball_directory}
      DATABASE_USER: ${DATABASE_USER:-postgres}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-postgres}
      # Add any other environment variables your app needs
      PROJECT_NAME: ${PROJECT_NAME:-Pinball Directory API}
      API_V1_STR: ${API_V1_STR:-/api/v1}
      DEBUG: ${DEBUG:-false}
    ports:
      - "${API_PORT:-8000}:8000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - pinball-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      # Mount source code for development (remove for production)
      - .:/app
      - /app/__pycache__
    # Override command for development with reload
    command: ["poetry", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

networks:
  pinball-network:
    external: true
