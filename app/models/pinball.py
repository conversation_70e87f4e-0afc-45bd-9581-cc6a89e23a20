from datetime import date
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Date, DateTime, Enum, ForeignKey, Integer, Numeric, String, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class PinballMachine(Base):
    __tablename__ = "pinball_machine"
    __table_args__ = {"schema": "pinball"}

    id = Column(Integer, primary_key=True, index=True)
    machine_name = Column(String(255), nullable=False)
    manufacturer = Column(String(100))
    year_manufactured = Column(Integer)
    model = Column(String(100))
    serial_number = Column(String(100), unique=True)
    theme = Column(String(255))
    game_type = Column(String(50))  # electromechanical, solid_state, digital
    player_count = Column(Integer, default=1)
    is_active = Column(Boolean, default=True)
    purchase_date = Column(Date)
    purchase_price = Column(Numeric(10, 2))
    current_value = Column(Numeric(10, 2))
    condition_rating = Column(Integer)
    notes = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    locations = relationship("PinballMachineLocation", back_populates="machine")
    maintenance_records = relationship("PinballMachineMaintenance", back_populates="machine")


class PinballMachineLocation(Base):
    __tablename__ = "pinball_machine_location"
    __table_args__ = {"schema": "pinball"}

    id = Column(Integer, primary_key=True, index=True)
    machine_id = Column(Integer, ForeignKey("pinball.pinball_machine.id", ondelete="CASCADE"), nullable=False)
    location_name = Column(String(255), nullable=False)
    location_type = Column(String(50))  # arcade, home, warehouse, repair_shop
    address_line1 = Column(String(255))
    address_line2 = Column(String(255))
    city = Column(String(100))
    state_province = Column(String(100))
    postal_code = Column(String(20))
    country = Column(String(100), default="USA")
    contact_person = Column(String(255))
    contact_phone = Column(String(20))
    contact_email = Column(String(255))
    placement_date = Column(Date, nullable=False)
    removal_date = Column(Date)
    is_current_location = Column(Boolean, default=True)
    location_notes = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    machine = relationship("PinballMachine", back_populates="locations")


class PinballMachineMaintenance(Base):
    __tablename__ = "pinball_machine_maintenance"
    __table_args__ = {"schema": "pinball"}

    id = Column(Integer, primary_key=True, index=True)
    machine_id = Column(Integer, ForeignKey("pinball.pinball_machine.id", ondelete="CASCADE"), nullable=False)
    maintenance_type = Column(String(50), nullable=False)  # routine, repair, upgrade, inspection
    maintenance_date = Column(Date, nullable=False)
    technician_name = Column(String(255))
    technician_company = Column(String(255))
    description = Column(Text, nullable=False)
    parts_replaced = Column(Text)
    parts_cost = Column(Numeric(10, 2))
    labor_cost = Column(Numeric(10, 2))
    total_cost = Column(Numeric(10, 2))
    maintenance_status = Column(String(20), default="completed")  # scheduled, in_progress, completed, cancelled
    priority_level = Column(String(20), default="normal")  # low, normal, high, urgent
    before_condition_rating = Column(Integer)
    after_condition_rating = Column(Integer)
    warranty_work = Column(Boolean, default=False)
    next_maintenance_due = Column(Date)
    maintenance_notes = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    machine = relationship("PinballMachine", back_populates="maintenance_records")
