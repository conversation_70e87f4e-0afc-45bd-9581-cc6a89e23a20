from datetime import date
from typing import Optional
from decimal import Decimal

from pydantic import BaseModel, EmailStr


class PinballMachineBase(BaseModel):
    machine_name: str
    manufacturer: Optional[str] = None
    year_manufactured: Optional[int] = None
    model: Optional[str] = None
    serial_number: Optional[str] = None
    theme: Optional[str] = None
    game_type: Optional[str] = None
    player_count: Optional[int] = 1
    is_active: bool = True
    purchase_date: Optional[date] = None
    purchase_price: Optional[Decimal] = None
    current_value: Optional[Decimal] = None
    condition_rating: Optional[int] = None
    notes: Optional[str] = None


class PinballMachineCreate(PinballMachineBase):
    pass


class PinballMachineUpdate(PinballMachineBase):
    pass


class PinballMachine(PinballMachineBase):
    id: int
    created_at: date
    updated_at: date

    class Config:
        from_attributes = True


class PinballMachineLocationBase(BaseModel):
    machine_id: int
    location_name: str
    location_type: Optional[str] = None
    address_line1: Optional[str] = None
    address_line2: Optional[str] = None
    city: Optional[str] = None
    state_province: Optional[str] = None
    postal_code: Optional[str] = None
    country: str = "USA"
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    placement_date: date
    removal_date: Optional[date] = None
    is_current_location: bool = True
    location_notes: Optional[str] = None


class PinballMachineLocationCreate(PinballMachineLocationBase):
    pass


class PinballMachineLocationUpdate(PinballMachineLocationBase):
    pass


class PinballMachineLocation(PinballMachineLocationBase):
    id: int
    created_at: date
    updated_at: date

    class Config:
        from_attributes = True


class PinballMaintenanceBase(BaseModel):
    machine_id: int
    maintenance_type: str
    maintenance_date: date
    technician_name: Optional[str] = None
    technician_company: Optional[str] = None
    description: str
    parts_replaced: Optional[str] = None
    parts_cost: Optional[Decimal] = None
    labor_cost: Optional[Decimal] = None
    total_cost: Optional[Decimal] = None
    maintenance_status: str = "completed"
    priority_level: str = "normal"
    before_condition_rating: Optional[int] = None
    after_condition_rating: Optional[int] = None
    warranty_work: bool = False
    next_maintenance_due: Optional[date] = None
    maintenance_notes: Optional[str] = None


class PinballMaintenanceCreate(PinballMaintenanceBase):
    pass


class PinballMaintenanceUpdate(PinballMaintenanceBase):
    pass


class PinballMaintenance(PinballMaintenanceBase):
    id: int
    created_at: date
    updated_at: date

    class Config:
        from_attributes = True
