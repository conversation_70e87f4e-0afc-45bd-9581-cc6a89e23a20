from datetime import date
from typing import Optional
from pydantic import BaseModel, EmailStr

# Pinball Machine Schemas
class PinballMachineBase(BaseModel):
    machine_name: str
    manufacturer: Optional[str] = None
    year_manufactured: Optional[int] = None
    model: Optional[str] = None
    serial_number: Optional[str] = None
    theme: Optional[str] = None
    game_type: Optional[str] = None
    player_count: Optional[int] = 1
    is_active: bool = True
    purchase_date: Optional[date] = None
    purchase_price: Optional[float] = None
    current_value: Optional[float] = None
    condition_rating: Optional[int] = None
    notes: Optional[str] = None

class PinballMachineCreate(PinballMachineBase):
    pass

class PinballMachine(PinballMachineBase):
    id: int
    created_at: date
    updated_at: date

    class Config:
        from_attributes = True

# Location Schemas
class LocationBase(BaseModel):
    machine_id: int
    location_name: str
    location_type: Optional[str] = None
    address_line1: Optional[str] = None
    address_line2: Optional[str] = None
    city: Optional[str] = None
    state_province: Optional[str] = None
    postal_code: Optional[str] = None
    country: str = "USA"
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None
    placement_date: date
    removal_date: Optional[date] = None
    is_current_location: bool = True
    location_notes: Optional[str] = None

class LocationCreate(LocationBase):
    pass

class Location(LocationBase):
    id: int
    created_at: date
    updated_at: date

    class Config:
        from_attributes = True

# Maintenance Schemas
class MaintenanceBase(BaseModel):
    machine_id: int
    maintenance_type: str
    maintenance_date: date
    technician_name: Optional[str] = None
    technician_company: Optional[str] = None
    description: str
    parts_replaced: Optional[str] = None
    parts_cost: Optional[float] = None
    labor_cost: Optional[float] = None
    total_cost: Optional[float] = None
    maintenance_status: str = "completed"
    priority_level: str = "normal"
    before_condition_rating: Optional[int] = None
    after_condition_rating: Optional[int] = None
    warranty_work: bool = False
    next_maintenance_due: Optional[date] = None
    maintenance_notes: Optional[str] = None

class MaintenanceCreate(MaintenanceBase):
    pass

class Maintenance(MaintenanceBase):
    id: int
    created_at: date
    updated_at: date

    class Config:
        from_attributes = True
