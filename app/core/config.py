from typing import Optional

from pydantic import PostgresDsn
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Pinball Directory API"
    
    PINBALL_DB_HOST: str
    PINBALL_DB_API_USER: str
    PINBALL_DB_API_USER_PASSWORD: str
    PINBALL_DB_NAME: str
    PINBALL_DB_PORT: str = "5432"

    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    def set_database_url(self) -> None:
        self.SQLALCHEMY_DATABASE_URI = (
            f"postgresql://{self.PINBALL_DB_API_USER}:{self.PINBALL_DB_API_USER_PASSWORD}"
            f"@{self.PINBALL_DB_HOST}:{self.PINBALL_DB_PORT}/{self.PINBALL_DB_NAME}"
        )

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
settings.set_database_url()
