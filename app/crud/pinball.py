from typing import List, Optional
from sqlalchemy.orm import Session

from app.models.pinball import PinballMachine, PinballMachineLocation, PinballMachineMaintenance
from app.schemas.pinball import PinballMachineCreate, PinballMachineUpdate, PinballMachineLocationCreate, PinballMachineLocationUpdate, PinballMaintenanceCreate, PinballMaintenanceUpdate


# Pinball Machine CRUD operations
def get_machine(db: Session, machine_id: int) -> Optional[PinballMachine]:
    return db.query(PinballMachine).filter(PinballMachine.id == machine_id).first()


def get_machine_by_serial(db: Session, serial_number: str) -> Optional[PinballMachine]:
    return db.query(PinballMachine).filter(PinballMachine.serial_number == serial_number).first()


def get_machines(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    is_active: Optional[bool] = None
) -> List[PinballMachine]:
    query = db.query(PinballMachine)
    if is_active is not None:
        query = query.filter(PinballMachine.is_active == is_active)
    return query.offset(skip).limit(limit).all()


def create_machine(db: Session, machine: PinballMachineCreate) -> PinballMachine:
    db_machine = PinballMachine(**machine.model_dump())
    db.add(db_machine)
    db.commit()
    db.refresh(db_machine)
    return db_machine


def update_machine(
    db: Session, 
    machine_id: int, 
    machine: PinballMachineUpdate
) -> Optional[PinballMachine]:
    db_machine = get_machine(db, machine_id)
    if db_machine:
        for key, value in machine.model_dump(exclude_unset=True).items():
            setattr(db_machine, key, value)
        db.commit()
        db.refresh(db_machine)
    return db_machine


def delete_machine(db: Session, machine_id: int) -> bool:
    db_machine = get_machine(db, machine_id)
    if db_machine:
        db.delete(db_machine)
        db.commit()
        return True
    return False


# Location CRUD operations
def get_location(db: Session, location_id: int) -> Optional[PinballMachineLocation]:
    return db.query(PinballMachineLocation).filter(PinballMachineLocation.id == location_id).first()


def get_locations(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    machine_id: Optional[int] = None,
    is_current: Optional[bool] = None
) -> List[PinballMachineLocation]:
    query = db.query(PinballMachineLocation)
    if machine_id:
        query = query.filter(PinballMachineLocation.machine_id == machine_id)
    if is_current is not None:
        query = query.filter(PinballMachineLocation.is_current_location == is_current)
    return query.offset(skip).limit(limit).all()


def create_location(db: Session, location: PinballMachineLocationCreate) -> PinballMachineLocation:
    if location.is_current_location:
        # Set all other locations for this machine to not current
        db.query(PinballMachineLocation).filter(
            PinballMachineLocation.machine_id == location.machine_id,
            PinballMachineLocation.is_current_location == True
        ).update({"is_current_location": False})
    
    db_location = PinballMachineLocation(**location.model_dump())
    db.add(db_location)
    db.commit()
    db.refresh(db_location)
    return db_location


def update_location(
    db: Session, 
    location_id: int, 
    location: PinballMachineLocationUpdate
) -> Optional[PinballMachineLocation]:
    db_location = get_location(db, location_id)
    if db_location:
        update_data = location.model_dump(exclude_unset=True)
        if "is_current_location" in update_data and update_data["is_current_location"]:
            # Set all other locations for this machine to not current
            db.query(PinballMachineLocation).filter(
                PinballMachineLocation.machine_id == db_location.machine_id,
                PinballMachineLocation.id != location_id,
                PinballMachineLocation.is_current_location == True
            ).update({"is_current_location": False})
        
        for key, value in update_data.items():
            setattr(db_location, key, value)
        db.commit()
        db.refresh(db_location)
    return db_location


def delete_location(db: Session, location_id: int) -> bool:
    db_location = get_location(db, location_id)
    if db_location:
        db.delete(db_location)
        db.commit()
        return True
    return False


# Maintenance CRUD operations
def get_maintenance(db: Session, maintenance_id: int) -> Optional[PinballMachineMaintenance]:
    return db.query(PinballMachineMaintenance).filter(PinballMachineMaintenance.id == maintenance_id).first()


def get_maintenance_records(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    machine_id: Optional[int] = None,
    maintenance_type: Optional[str] = None,
    maintenance_status: Optional[str] = None
) -> List[PinballMachineMaintenance]:
    query = db.query(PinballMachineMaintenance)
    if machine_id:
        query = query.filter(PinballMachineMaintenance.machine_id == machine_id)
    if maintenance_type:
        query = query.filter(PinballMachineMaintenance.maintenance_type == maintenance_type)
    if maintenance_status:
        query = query.filter(PinballMachineMaintenance.maintenance_status == maintenance_status)
    return query.offset(skip).limit(limit).all()


def create_maintenance(db: Session, maintenance: PinballMaintenanceCreate) -> PinballMachineMaintenance:
    db_maintenance = PinballMachineMaintenance(**maintenance.model_dump())
    db.add(db_maintenance)
    db.commit()
    db.refresh(db_maintenance)
    return db_maintenance


def update_maintenance(
    db: Session, 
    maintenance_id: int, 
    maintenance: PinballMaintenanceUpdate
) -> Optional[PinballMachineMaintenance]:
    db_maintenance = get_maintenance(db, maintenance_id)
    if db_maintenance:
        for key, value in maintenance.model_dump(exclude_unset=True).items():
            setattr(db_maintenance, key, value)
        db.commit()
        db.refresh(db_maintenance)
    return db_maintenance


def delete_maintenance(db: Session, maintenance_id: int) -> bool:
    db_maintenance = get_maintenance(db, maintenance_id)
    if db_maintenance:
        db.delete(db_maintenance)
        db.commit()
        return True
    return False
