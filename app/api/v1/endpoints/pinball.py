from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import SessionLocal
from app.crud import pinball as crud
from app.schemas.pinball import (
    PinballMachine, PinballMachineCreate, PinballMachineUpdate,
    PinballMachineLocation, PinballMachineLocationCreate, PinballMachineLocationUpdate,
    PinballMaintenance, PinballMaintenanceCreate, PinballMaintenanceUpdate
)

router = APIRouter()


# Dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Pinball Machine endpoints
@router.post("/machines/", response_model=PinballMachine)
def create_pinball_machine(
    machine: PinballMachineCreate,
    db: Session = Depends(get_db)
):
    if machine.serial_number:
        db_machine = crud.get_machine_by_serial(db, serial_number=machine.serial_number)
        if db_machine:
            raise HTTPException(status_code=400, detail="Serial number already registered")
    return crud.create_machine(db=db, machine=machine)


@router.get("/machines/", response_model=List[PinballMachine])
def read_pinball_machines(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    machines = crud.get_machines(db, skip=skip, limit=limit, is_active=is_active)
    return machines


@router.get("/machines/{machine_id}", response_model=PinballMachine)
def read_pinball_machine(machine_id: int, db: Session = Depends(get_db)):
    db_machine = crud.get_machine(db, machine_id=machine_id)
    if db_machine is None:
        raise HTTPException(status_code=404, detail="Pinball machine not found")
    return db_machine


@router.put("/machines/{machine_id}", response_model=PinballMachine)
def update_pinball_machine(
    machine_id: int,
    machine: PinballMachineUpdate,
    db: Session = Depends(get_db)
):
    db_machine = crud.update_machine(db, machine_id=machine_id, machine=machine)
    if db_machine is None:
        raise HTTPException(status_code=404, detail="Pinball machine not found")
    return db_machine


@router.delete("/machines/{machine_id}")
def delete_pinball_machine(machine_id: int, db: Session = Depends(get_db)):
    success = crud.delete_machine(db, machine_id=machine_id)
    if not success:
        raise HTTPException(status_code=404, detail="Pinball machine not found")
    return {"detail": "Machine successfully deleted"}


# Location endpoints
@router.post("/locations/", response_model=PinballMachineLocation)
def create_location(
    location: PinballMachineLocationCreate,
    db: Session = Depends(get_db)
):
    # Verify machine exists
    machine = crud.get_machine(db, machine_id=location.machine_id)
    if not machine:
        raise HTTPException(status_code=404, detail="Machine not found")
    return crud.create_location(db=db, location=location)


@router.get("/locations/", response_model=List[PinballMachineLocation])
def read_locations(
    skip: int = 0,
    limit: int = 100,
    machine_id: Optional[int] = None,
    is_current: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    locations = crud.get_locations(
        db, skip=skip, limit=limit, 
        machine_id=machine_id, is_current=is_current
    )
    return locations


@router.get("/locations/{location_id}", response_model=PinballMachineLocation)
def read_location(location_id: int, db: Session = Depends(get_db)):
    db_location = crud.get_location(db, location_id=location_id)
    if db_location is None:
        raise HTTPException(status_code=404, detail="Location not found")
    return db_location


@router.put("/locations/{location_id}", response_model=PinballMachineLocation)
def update_location(
    location_id: int,
    location: PinballMachineLocationUpdate,
    db: Session = Depends(get_db)
):
    db_location = crud.update_location(db, location_id=location_id, location=location)
    if db_location is None:
        raise HTTPException(status_code=404, detail="Location not found")
    return db_location


@router.delete("/locations/{location_id}")
def delete_location(location_id: int, db: Session = Depends(get_db)):
    success = crud.delete_location(db, location_id=location_id)
    if not success:
        raise HTTPException(status_code=404, detail="Location not found")
    return {"detail": "Location successfully deleted"}


# Maintenance endpoints
@router.post("/maintenance/", response_model=PinballMaintenance)
def create_maintenance(
    maintenance: PinballMaintenanceCreate,
    db: Session = Depends(get_db)
):
    # Verify machine exists
    machine = crud.get_machine(db, machine_id=maintenance.machine_id)
    if not machine:
        raise HTTPException(status_code=404, detail="Machine not found")
    return crud.create_maintenance(db=db, maintenance=maintenance)


@router.get("/maintenance/", response_model=List[PinballMaintenance])
def read_maintenance_records(
    skip: int = 0,
    limit: int = 100,
    machine_id: Optional[int] = None,
    maintenance_type: Optional[str] = None,
    maintenance_status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    maintenance_records = crud.get_maintenance_records(
        db, skip=skip, limit=limit,
        machine_id=machine_id,
        maintenance_type=maintenance_type,
        maintenance_status=maintenance_status
    )
    return maintenance_records


@router.get("/maintenance/{maintenance_id}", response_model=PinballMaintenance)
def read_maintenance(maintenance_id: int, db: Session = Depends(get_db)):
    db_maintenance = crud.get_maintenance(db, maintenance_id=maintenance_id)
    if db_maintenance is None:
        raise HTTPException(status_code=404, detail="Maintenance record not found")
    return db_maintenance


@router.put("/maintenance/{maintenance_id}", response_model=PinballMaintenance)
def update_maintenance(
    maintenance_id: int,
    maintenance: PinballMaintenanceUpdate,
    db: Session = Depends(get_db)
):
    db_maintenance = crud.update_maintenance(
        db, maintenance_id=maintenance_id, maintenance=maintenance
    )
    if db_maintenance is None:
        raise HTTPException(status_code=404, detail="Maintenance record not found")
    return db_maintenance


@router.delete("/maintenance/{maintenance_id}")
def delete_maintenance(maintenance_id: int, db: Session = Depends(get_db)):
    success = crud.delete_maintenance(db, maintenance_id=maintenance_id)
    if not success:
        raise HTTPException(status_code=404, detail="Maintenance record not found")
    return {"detail": "Maintenance record successfully deleted"}
