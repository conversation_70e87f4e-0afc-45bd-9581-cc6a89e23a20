[tool.poetry]
name = "pinball-directory-api"
version = "1.0.0"
description = "2026 Pinball Directory API"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
pyyaml = "^6.0.2"
fastapi = "^0.110.0"
uvicorn = "^0.27.1"
sqlalchemy = "^2.0.27"
psycopg2-binary = "^2.9.9"
python-dotenv = "^1.0.1"
pydantic = {extras = ["email"], version = "^2.6.1"}
doppler-env = "^0.3.1"
pydantic-settings = "^2.2.1"

[tool.poetry.group.dev.dependencies]
flake8 = "^7.1"
isort = "^5.13"
vulture = "^2.5"
black = "^24.10"
ipdb = "^0.13.13"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 150
exclude = ".*.sql|.*.md|.*.pyc|.*.yaml|.*.yml|.*.json|registration|do-not-commit|venv"

[tool.isort]
profile = "black"
float_to_top = true
skip = [".gitignore", ".dockerignore", ".venv"]
skip_glob = ["**/.venv/**", "**/registration/**"]
extend_skip = [".venv"]
